import React from 'react';
import { AbsoluteFill, useCurrentFrame, interpolate } from 'remotion';
import AnimatedText from '../components/AnimatedText';
import DataChart from '../components/DataChart';
import { LightRays } from '../components/LightRays';
import { SceneProps } from '../types';

export const SleepScienceScene: React.FC<SceneProps> = ({ frame, durationInFrames }) => {
  const currentFrame = useCurrentFrame();

  // 睡眠数据
  const sleepData = [
    { label: '7-9小时', value: 85, color: '#10B981' },
    { label: '6小时以下', value: 45, color: '#EF4444' },
    { label: '9小时以上', value: 70, color: '#F59E0B' },
  ];

  const regularityData = [
    { label: '规律作息', value: 90, color: '#3B82F6' },
    { label: '不规律作息', value: 40, color: '#EF4444' },
  ];

  // 计算文字和数据出现进度，用于控制光线效果
  const textProgress = interpolate(
    currentFrame,
    [0, 60, 150, 210, 360, 450],
    [0, 0.2, 0.4, 0.6, 0.8, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <AbsoluteFill className="bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900">
      {/* Light Rays 背景效果 */}
      <LightRays
        raysOrigin="top-center"
        raysColor="#3B82F6"
        raysSpeed={1.2}
        lightSpread={1.0 + textProgress * 0.5}
        rayLength={1.5 + textProgress * 0.5}
        noiseAmount={0.15}
        distortion={0.08}
        fadeDistance={0.5 + textProgress * 0.5}
        saturation={0.7 + textProgress * 0.3}
        className="opacity-70"
      />
      {/* 标题部分 */}
      <div className="absolute top-16 left-1/2 transform -translate-x-1/2 text-center">
        <AnimatedText
          text="睡眠的核心不是早，而是规律"
          startFrame={0}
          className="text-5xl font-bold text-white mb-4"
          animationType="reveal"
          duration={60}
        />
        
        <AnimatedText
          text="研究表明，成年人平均需要 7–9 小时的睡眠"
          startFrame={60}
          className="text-2xl text-gray-200"
          animationType="fade"
          duration={50}
        />
      </div>

      {/* 主要内容区域 */}
      <div className="flex items-center justify-center h-full pt-32 pb-16">
        <div className="grid grid-cols-2 gap-16 max-w-6xl">
          {/* 左侧：睡眠时长数据 */}
          <div 
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-8"
            style={{
              opacity: interpolate(currentFrame, [120, 150], [0, 1], {
                extrapolateLeft: 'clamp',
                extrapolateRight: 'clamp',
              }),
              transform: `translateY(${interpolate(
                currentFrame,
                [120, 150],
                [50, 0],
                { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
              )}px)`,
            }}
          >
            <DataChart
              data={sleepData}
              startFrame={150}
              type="bar"
              width={350}
              height={250}
              title="睡眠时长与健康指数"
            />
          </div>

          {/* 右侧：规律性数据 */}
          <div 
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-8"
            style={{
              opacity: interpolate(currentFrame, [180, 210], [0, 1], {
                extrapolateLeft: 'clamp',
                extrapolateRight: 'clamp',
              }),
              transform: `translateY(${interpolate(
                currentFrame,
                [180, 210],
                [50, 0],
                { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
              )}px)`,
            }}
          >
            <DataChart
              data={regularityData}
              startFrame={210}
              type="bar"
              width={350}
              height={250}
              title="作息规律性影响"
            />
          </div>
        </div>
      </div>

      {/* 关键要点 */}
      <div className="absolute bottom-32 left-1/2 transform -translate-x-1/2 w-full max-w-5xl px-8">
        <div className="grid grid-cols-3 gap-8 text-center">
          <div 
            className="bg-white/10 backdrop-blur-sm rounded-xl p-6"
            style={{
              opacity: interpolate(currentFrame, [270, 300], [0, 1], {
                extrapolateLeft: 'clamp',
                extrapolateRight: 'clamp',
              }),
            }}
          >
            <div className="text-3xl mb-2">⏰</div>
            <AnimatedText
              text="入睡和起床时间是否稳定"
              startFrame={300}
              className="text-lg font-semibold text-white"
              animationType="fade"
              duration={40}
            />
          </div>

          <div 
            className="bg-white/10 backdrop-blur-sm rounded-xl p-6"
            style={{
              opacity: interpolate(currentFrame, [300, 330], [0, 1], {
                extrapolateLeft: 'clamp',
                extrapolateRight: 'clamp',
              }),
            }}
          >
            <div className="text-3xl mb-2">⏳</div>
            <AnimatedText
              text="是否保证足够的睡眠时长"
              startFrame={330}
              className="text-lg font-semibold text-white"
              animationType="fade"
              duration={40}
            />
          </div>

          <div 
            className="bg-white/10 backdrop-blur-sm rounded-xl p-6"
            style={{
              opacity: interpolate(currentFrame, [330, 360], [0, 1], {
                extrapolateLeft: 'clamp',
                extrapolateRight: 'clamp',
              }),
            }}
          >
            <div className="text-3xl mb-2">🧠</div>
            <AnimatedText
              text="是否获得完整的深睡和快速眼动睡眠"
              startFrame={360}
              className="text-lg font-semibold text-white"
              animationType="fade"
              duration={40}
            />
          </div>
        </div>
      </div>

      {/* 举例说明 */}
      <div 
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-center"
        style={{
          opacity: interpolate(currentFrame, [420, 450], [0, 1], {
            extrapolateLeft: 'clamp',
            extrapolateRight: 'clamp',
          }),
        }}
      >
        <AnimatedText
          text="举个例子：每天凌晨1点睡、早上9点起，保持规律，也比今天10点睡明天2点睡更健康"
          startFrame={450}
          className="text-xl text-yellow-300 font-medium max-w-4xl"
          animationType="typewriter"
          duration={120}
        />
      </div>
    </AbsoluteFill>
  );
};

export default SleepScienceScene;
