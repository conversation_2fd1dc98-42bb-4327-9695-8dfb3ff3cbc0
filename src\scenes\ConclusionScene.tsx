import React from 'react';
import { AbsoluteFill, useCurrentFrame, interpolate } from 'remotion';
import AnimatedText from '../components/AnimatedText';
import { LightRays } from '../components/LightRays';
import { SceneProps } from '../types';

export const ConclusionScene: React.FC<SceneProps> = ({ frame, durationInFrames }) => {
  const currentFrame = useCurrentFrame();

  // 计算文字出现进度，用于控制光线效果
  const textProgress = interpolate(
    currentFrame,
    [0, 60, 120, 180, 240, 300, 360, 420, 480],
    [0, 0.1, 0.2, 0.4, 0.6, 0.7, 0.8, 0.9, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <AbsoluteFill className="bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
      {/* Light Rays 背景效果 */}
      <LightRays
        raysOrigin="top-center"
        raysColor="#A855F7"
        raysSpeed={0.8}
        lightSpread={1.2 + textProgress * 0.8}
        rayLength={1.6 + textProgress * 0.4}
        noiseAmount={0.08}
        distortion={0.04}
        fadeDistance={0.8 + textProgress * 0.2}
        saturation={0.9 + textProgress * 0.1}
        pulsating={textProgress > 0.5}
        className="opacity-85"
      />
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        {[...Array(30)].map((_, i) => {
          const delay = i * 8;
          const opacity = interpolate(
            currentFrame,
            [delay, delay + 60],
            [0, 0.4],
            {
              extrapolateLeft: 'clamp',
              extrapolateRight: 'clamp',
            }
          );
          
          return (
            <div
              key={i}
              className="absolute w-1 h-1 bg-white rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                opacity,
                animation: `twinkle ${2 + Math.random() * 3}s ease-in-out infinite`,
              }}
            />
          );
        })}
      </div>

      {/* 主要内容 */}
      <div className="flex flex-col items-center justify-center h-full text-center px-8 space-y-12">
        {/* 核心问题回顾 */}
        <div 
          style={{
            opacity: interpolate(currentFrame, [0, 30], [0, 1], {
              extrapolateLeft: 'clamp',
              extrapolateRight: 'clamp',
            }),
            transform: `scale(${interpolate(
              currentFrame,
              [0, 30],
              [0.8, 1],
              { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
            )})`,
          }}
        >
          <AnimatedText
            text="早睡是伪命题吗？"
            startFrame={30}
            className="text-6xl font-bold text-white mb-8"
            animationType="reveal"
            duration={60}
          />
        </div>

        {/* 答案分析 */}
        <div className="max-w-5xl space-y-8">
          {/* 第一个观点 */}
          <div 
            className="bg-red-500/20 border-2 border-red-400 rounded-2xl p-8"
            style={{
              opacity: interpolate(currentFrame, [120, 150], [0, 1], {
                extrapolateLeft: 'clamp',
                extrapolateRight: 'clamp',
              }),
              transform: `translateX(${interpolate(
                currentFrame,
                [120, 150],
                [-100, 0],
                { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
              )}px)`,
            }}
          >
            <div className="flex items-center justify-center mb-4">
              <span className="text-4xl mr-4">❌</span>
              <AnimatedText
                text="如果你把它当成万能钥匙"
                startFrame={150}
                className="text-3xl font-bold text-red-300"
                animationType="fade"
                duration={50}
              />
            </div>
            <AnimatedText
              text="它确实是伪命题"
              startFrame={180}
              className="text-2xl text-white font-semibold"
              animationType="typewriter"
              duration={40}
            />
          </div>

          {/* 第二个观点 */}
          <div 
            className="bg-green-500/20 border-2 border-green-400 rounded-2xl p-8"
            style={{
              opacity: interpolate(currentFrame, [220, 250], [0, 1], {
                extrapolateLeft: 'clamp',
                extrapolateRight: 'clamp',
              }),
              transform: `translateX(${interpolate(
                currentFrame,
                [220, 250],
                [100, 0],
                { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
              )}px)`,
            }}
          >
            <div className="flex items-center justify-center mb-4">
              <span className="text-4xl mr-4">✅</span>
              <AnimatedText
                text="如果理解为保持规律作息、尊重生物钟"
                startFrame={250}
                className="text-3xl font-bold text-green-300"
                animationType="fade"
                duration={60}
              />
            </div>
            <AnimatedText
              text="那它就是改善生活的起点"
              startFrame={290}
              className="text-2xl text-white font-semibold"
              animationType="typewriter"
              duration={50}
            />
          </div>
        </div>

        {/* 核心洞察 */}
        <div 
          className="bg-gradient-to-r from-purple-500/30 to-pink-500/30 backdrop-blur-sm rounded-3xl p-12 max-w-4xl"
          style={{
            opacity: interpolate(currentFrame, [380, 410], [0, 1], {
              extrapolateLeft: 'clamp',
              extrapolateRight: 'clamp',
            }),
            transform: `scale(${interpolate(
              currentFrame,
              [380, 410],
              [0.9, 1],
              { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
            )})`,
          }}
        >
          <div className="text-5xl mb-6">💡</div>
          <AnimatedText
            text="真正的健康不是靠单一习惯"
            startFrame={410}
            className="text-3xl font-bold text-yellow-300 mb-4"
            animationType="reveal"
            duration={60}
          />
          <AnimatedText
            text="而是建立在科学认知和系统性生活方式之上"
            startFrame={450}
            className="text-2xl text-white leading-relaxed"
            animationType="typewriter"
            duration={80}
          />
        </div>

        {/* 行动建议 */}
        <div 
          className="grid grid-cols-3 gap-6 max-w-6xl"
          style={{
            opacity: interpolate(currentFrame, [550, 580], [0, 1], {
              extrapolateLeft: 'clamp',
              extrapolateRight: 'clamp',
            }),
          }}
        >
          {[
            { icon: '🕐', text: '建立规律作息', delay: 0 },
            { icon: '💤', text: '关注睡眠质量', delay: 20 },
            { icon: '🌱', text: '培养健康习惯', delay: 40 }
          ].map((item, index) => (
            <div 
              key={index}
              className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center"
              style={{
                transform: `translateY(${interpolate(
                  currentFrame,
                  [580 + item.delay, 580 + item.delay + 30],
                  [50, 0],
                  { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
                )}px)`,
              }}
            >
              <div className="text-4xl mb-3">{item.icon}</div>
              <AnimatedText
                text={item.text}
                startFrame={580 + item.delay + 20}
                className="text-lg font-semibold text-white"
                animationType="fade"
                duration={30}
              />
            </div>
          ))}
        </div>

        {/* 最终总结 */}
        <div 
          style={{
            opacity: interpolate(currentFrame, [680, 710], [0, 1], {
              extrapolateLeft: 'clamp',
              extrapolateRight: 'clamp',
            }),
          }}
        >
          <AnimatedText
            text="记住：早睡不是终点，而是健康生活的起点"
            startFrame={710}
            className="text-3xl font-bold text-gradient bg-gradient-to-r from-yellow-300 to-pink-300 bg-clip-text text-transparent"
            animationType="typewriter"
            duration={100}
          />
        </div>
      </div>

      {/* 感谢观看 */}
      <div 
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-center"
        style={{
          opacity: interpolate(currentFrame, [durationInFrames - 60, durationInFrames - 30], [0, 1], {
            extrapolateLeft: 'clamp',
            extrapolateRight: 'clamp',
          }),
        }}
      >
        <div className="text-white/70 text-lg">
          感谢观看 | 关注更多科学生活内容
        </div>
      </div>
    </AbsoluteFill>
  );
};

export default ConclusionScene;
